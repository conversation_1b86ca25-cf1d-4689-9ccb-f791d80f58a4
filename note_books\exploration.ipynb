# Cell 1: Setup
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split

# Cell 2: Load Data
train_data = pd.read_csv('../data/train.csv')
print(f"Data shape: {train_data.shape}")

# Cell 3: Target Analysis
plt.figure(figsize=(10, 6))
sns.histplot(train_data['SalePrice'], kde=True)
plt.title('Distribution of SalePrice')
plt.show()

# Cell 4: Missing Values Analysis
missing = train_data.isnull().sum()
missing = missing[missing > 0]
missing.sort_values(ascending=False, inplace=True)
missing.plot(kind='bar', figsize=(12, 6))
plt.title('Missing Values per Feature')
plt.show()

# Contoh kode di notebook untuk EDA
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load data
train_data = pd.read_csv('../data/train.csv')

# Analisis dasar
print("Shape:", train_data.shape)
print("Info:")
train_data.info()

# Distribusi target variable (SalePrice)
plt.figure(figsize=(10, 6))
sns.histplot(train_data['SalePrice'], kde=True)
plt.title('Distribution of SalePrice')
plt.show()